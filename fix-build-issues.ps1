# PowerShell script to fix EAS build issues
Write-Host "Fixing EAS Build Issues..." -ForegroundColor Green

# Step 1: Stop any running processes that might be locking files
Write-Host "Stopping any running Node/Expo processes..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*node*" -or $_.ProcessName -like "*expo*" -or $_.ProcessName -like "*metro*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# Step 2: Wait a moment for processes to fully stop
Start-Sleep -Seconds 3

# Step 3: Try to remove node_modules using different methods
Write-Host "Attempting to clean node_modules..." -ForegroundColor Yellow

# Method 1: Try PowerShell Remove-Item
try {
    Remove-Item -Recurse -Force node_modules -ErrorAction Stop
    Write-Host "Successfully removed node_modules with PowerShell" -ForegroundColor Green
} catch {
    Write-Host "PowerShell removal failed, trying alternative methods..." -ForegroundColor Yellow
    
    # Method 2: Try using robocopy to delete (Windows-specific trick)
    try {
        New-Item -ItemType Directory -Path "empty_temp_dir" -Force | Out-Null
        robocopy "empty_temp_dir" "node_modules" /MIR /R:0 /W:0 | Out-Null
        Remove-Item -Recurse -Force "empty_temp_dir" -ErrorAction SilentlyContinue
        Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
        Write-Host "Successfully removed node_modules with robocopy" -ForegroundColor Green
    } catch {
        Write-Host "Robocopy method also failed" -ForegroundColor Red
    }
}

# Step 4: Remove package-lock.json
Remove-Item package-lock.json -Force -ErrorAction SilentlyContinue

# Step 5: Clear npm cache
Write-Host "Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force

# Step 6: Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
npm install

Write-Host "Build fix script completed!" -ForegroundColor Green
Write-Host "You can now try running: eas build --platform android --profile development" -ForegroundColor Cyan
