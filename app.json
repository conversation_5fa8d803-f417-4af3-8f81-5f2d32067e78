{"expo": {"plugins": ["expo-dev-client"], "android": {"package": "com.synaptix.app", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.VIBRATE", "android.permission.SYSTEM_ALERT_WINDOW"], "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#121212"}}, "name": "Synaptix", "slug": "synaptix", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": false, "splash": {"image": "./assets/logo_splashscreen-removebg-preview.png", "resizeMode": "contain", "backgroundColor": "#121212"}, "jsEngine": "hermes", "scheme": "synaptix", "ios": {"supportsTablet": true, "bundleIdentifier": "com.synaptix.app", "infoPlist": {}}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "bce97cf0-0199-4ed8-940f-c58f931c8a9e"}}}}