# EAS Build Troubleshooting Guide

## Current Issue
You're experiencing a file locking issue on Windows that prevents proper dependency installation, causing EAS builds to fail.

## Immediate Solutions to Try

### 1. Restart and Clean Environment
```powershell
# 1. Restart your computer
# 2. Open PowerShell as Administrator
# 3. Navigate to your project directory
cd "C:\Users\<USER>\Projects\BrainApp"

# 4. Temporarily disable Windows Defender (optional)
Set-MpPreference -DisableRealtimeMonitoring $true

# 5. Clean and install
Remove-Item node_modules -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item package-lock.json -Force -ErrorAction SilentlyContinue
npm cache clean --force
npm install

# 6. Try EAS build
eas build --platform android --profile development

# 7. Re-enable Windows Defender
Set-MpPreference -DisableRealtimeMonitoring $false
```

### 2. Alternative Build Location
```powershell
# Create a new temporary location
mkdir C:\temp\BrainApp
cd C:\temp\BrainApp

# Copy your source files (excluding node_modules)
Copy-Item "C:\Users\<USER>\Projects\BrainApp\*" -Destination . -Recurse -Exclude node_modules

# Install dependencies and build
npm install
eas build --platform android --profile development
```

### 3. Use Different Package Manager
```powershell
# Install pnpm (often handles Windows file locking better)
npm install -g pnpm

# Use pnpm instead
pnpm install
eas build --platform android --profile development
```

## Configuration Changes Made

The following optimizations have been applied to your project:

### app.json Changes:
- Added `expo-dev-client` plugin
- Enabled Hermes JS engine
- Added proper Android permissions

### eas.json Changes:
- Added development environment variables
- Set proper build type (apk)

### android/gradle.properties Changes:
- Enabled Hermes
- Added build optimizations
- Enabled AndroidX and Jetifier

## If All Else Fails

### Option 1: Use GitHub Codespaces
1. Push your code to GitHub
2. Open in GitHub Codespaces
3. Run the build from the cloud environment

### Option 2: Use a Different Computer
Try running the build from a different Windows machine or a Mac/Linux system.

### Option 3: Contact Expo Support
If the issue persists, this might be a specific Windows environment issue that Expo support can help with.

## Common Windows-Specific Issues

1. **Antivirus Interference**: Windows Defender or third-party antivirus
2. **Path Length Limits**: Windows has a 260-character path limit
3. **File Permissions**: UAC or permission issues
4. **OneDrive Sync**: File sync conflicts
5. **Process Handles**: Zombie processes holding file handles

## Next Steps

1. Try Solution 1 (restart and clean environment) first
2. If that fails, try Solution 2 (different location)
3. Consider using WSL or a different development environment
4. Contact Expo support if the issue persists

The configurations have been optimized for your development build, so once the dependency installation issue is resolved, the build should succeed.
