import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Modal,
  Pressable,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from '../contexts/AuthContext';
import { validateEmail, validateName, validateDOB, validatePassword } from '../utils/validation';

import GradientButton from '../components/GradientButton';

export default function SignupScreen({ navigation }) {
  const {
    signup,
    skipToHome,
    currentUser
  } = useAuth();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    dob: '',
    gender: 'male',
    password: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState({});
  const [authError, setAuthError] = useState(''); // New state for authentication errors
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [date, setDate] = useState(new Date());
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSkipping, setIsSkipping] = useState(false);

  const updateFormData = (field, value) => {
    setFormData({
      ...formData,
      [field]: value,
    });

    // Clear error when user types
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: '',
      });
    }

    // Clear auth error when user makes any changes
    if (authError) {
      setAuthError('');
    }

    // Real-time validation for email
    if (field === 'email' && value) {
      if (!validateEmail(value)) {
        setErrors(prev => ({
          ...prev,
          email: 'Please enter a valid email (e.g., <EMAIL>)'
        }));
      }
    }
  };

  // Using validation functions from utils/validation.js

  const onDateChange = (event, selectedDate) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'ios');
    setDate(currentDate);

    // Format date as MM/DD/YYYY with proper padding
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const day = currentDate.getDate().toString().padStart(2, '0');
    const year = currentDate.getFullYear();

    const formattedDate = `${month}/${day}/${year}`;
    updateFormData('dob', formattedDate);

    // Clear any DOB errors since we're using the date picker
    if (errors.dob) {
      setErrors(prev => ({
        ...prev,
        dob: ''
      }));
    }
  };

  const handleSignup = async () => {
    const newErrors = {};

    // Clear any previous auth errors
    setAuthError('');

    // Validate name
    if (!validateName(formData.name)) {
      newErrors.name = 'Name is required and must be at least 2 characters';
    }

    // Validate email
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email (e.g., <EMAIL>)';
    }

    // Validate DOB
    if (!formData.dob) {
      newErrors.dob = 'Date of birth is required';
    } else if (!validateDOB(formData.dob)) {
      newErrors.dob = 'Please enter a valid date (MM/DD/YYYY)';
    }

    // Validate password
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      newErrors.password = passwordValidation.message;
    }

    // Validate confirm password
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);

    // If no errors, proceed with signup
    if (Object.keys(newErrors).length === 0) {
      setIsLoading(true);
      try {
        // Call signup function from auth context
        const result = await signup(formData);

        if (result.success) {
          // Navigation is handled automatically by AppNavigator based on currentUser state
          // No manual navigation needed here
        } else {
          // Display the specific error message in the UI
          setAuthError(result.error || 'Registration failed. Please try again.');
        }
      } catch (error) {
        setAuthError('An unexpected error occurred. Please try again.');
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle skip to home
  const handleSkip = async () => {
    setIsSkipping(true);
    try {
      const result = await skipToHome();
      if (result.success) {
        // Navigation is handled automatically by AppNavigator based on currentUser state
        // No manual navigation needed here
      } else {
        setAuthError('Failed to skip to home. Please try again.');
      }
    } catch (error) {
      setAuthError('An unexpected error occurred. Please try again.');
      console.error(error);
    } finally {
      setIsSkipping(false);
    }
  };

  // Reference to the ScrollView
  const scrollViewRef = useRef();

  // Function to scroll to a specific field with dynamic positioning
  const scrollToField = (fieldPosition) => {
    // Add a longer delay to ensure the keyboard is fully shown
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ y: fieldPosition, animated: true });
      }
    }, 500); // Increased delay for better keyboard handling
  };

  // Function to scroll to the confirm password field
  const scrollToConfirmPassword = () => {
    // Use a callback to get the current scroll position and add an offset
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      enabled
    >
      <StatusBar barStyle="light-content" />
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        keyboardDismissMode="on-drag">
        <View style={styles.header}>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>Please fill in your details</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={[styles.input, errors.name ? styles.inputError : null]}
            placeholder="Enter your full name"
            placeholderTextColor="#666"
            value={formData.name}
            onChangeText={(text) => updateFormData('name', text)}
            blurOnSubmit={false}
            returnKeyType="next"
          />
          {errors.name ? <Text style={styles.errorText}>{errors.name}</Text> : null}

          <Text style={styles.label}>Email</Text>
          <TextInput
            style={[styles.input, errors.email ? styles.inputError : null]}
            placeholder="Enter your email"
            placeholderTextColor="#666"
            keyboardType="email-address"
            autoCapitalize="none"
            value={formData.email}
            onChangeText={(text) => updateFormData('email', text)}
            blurOnSubmit={false}
            returnKeyType="next"
          />
          {errors.email ? <Text style={styles.errorText}>{errors.email}</Text> : null}

          <Text style={styles.label}>Date of Birth</Text>
          {Platform.OS === 'web' ? (
            // Web-specific date input
            <View>
              <TextInput
                style={[styles.input, errors.dob ? styles.inputError : null]}
                placeholder="MM/DD/YYYY"
                placeholderTextColor="#666"
                value={formData.dob}
                onChangeText={(text) => updateFormData('dob', text)}
                keyboardType="numeric"
                maxLength={10}
              />
              <Text style={styles.helperText}>Format: MM/DD/YYYY (e.g., 01/31/1990)</Text>
            </View>
          ) : (
            // Native date picker for mobile
            <>
              <TouchableOpacity
                style={[styles.input, errors.dob ? styles.inputError : null]}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={formData.dob ? styles.dateText : styles.placeholderText}>
                  {formData.dob || "Select your date of birth"}
                </Text>
              </TouchableOpacity>
              {showDatePicker && (
                <DateTimePicker
                  value={date}
                  mode="date"
                  display="default"
                  onChange={onDateChange}
                  maximumDate={new Date()}
                  minimumDate={new Date(1920, 0, 1)}
                />
              )}
            </>
          )}
          {errors.dob ? <Text style={styles.errorText}>{errors.dob}</Text> : null}

          <Text style={styles.label}>Gender</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={formData.gender}
              onValueChange={(value) => updateFormData('gender', value)}
              style={styles.picker}
              dropdownIconColor="#ffffff"
            >
              <Picker.Item label="Male" value="male" />
              <Picker.Item label="Female" value="female" />
              <Picker.Item label="Other" value="other" />
              <Picker.Item label="Prefer not to say" value="not_specified" />
            </Picker>
          </View>

          <Text style={styles.label}>Password</Text>
          <View style={styles.passwordContainer}>
            <TextInput
              style={[styles.passwordInput, errors.password ? styles.inputError : null]}
              placeholder="Create a password"
              placeholderTextColor="#666"
              secureTextEntry={!showPassword}
              value={formData.password}
              onChangeText={(text) => updateFormData('password', text)}
              onFocus={() => scrollToField(400)}
              blurOnSubmit={false}
              returnKeyType="next"
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Text style={styles.eyeIconText}>{showPassword ? '👁️' : '👁️‍🗨️'}</Text>
            </TouchableOpacity>
          </View>
          {errors.password ? <Text style={styles.errorText}>{errors.password}</Text> : null}

          <Text style={styles.label}>Confirm Password</Text>
          <View style={styles.passwordContainer}>
            <TextInput
              style={[styles.passwordInput, errors.confirmPassword ? styles.inputError : null]}
              placeholder="Confirm your password"
              placeholderTextColor="#666"
              secureTextEntry={!showConfirmPassword}
              value={formData.confirmPassword}
              onChangeText={(text) => updateFormData('confirmPassword', text)}
              onFocus={scrollToConfirmPassword}
              blurOnSubmit={true}
              returnKeyType="done"
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <Text style={styles.eyeIconText}>{showConfirmPassword ? '👁️' : '👁️‍🗨️'}</Text>
            </TouchableOpacity>
          </View>
          {errors.confirmPassword ? (
            <Text style={styles.errorText}>{errors.confirmPassword}</Text>
          ) : null}

          {/* Authentication Error Message */}
          {authError ? (
            <View style={styles.authErrorContainer}>
              <Text style={styles.authErrorText}>{authError}</Text>
            </View>
          ) : null}

          {/* Remember me checkbox removed - now handled by the save prompt */}

          <GradientButton
            text={isLoading ? 'REGISTERING...' : 'REGISTER'}
            onPress={handleSignup}
            disabled={isLoading || isSkipping}
            style={styles.signupButton}
          />

          {/* Skip Button */}
          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkip}
            disabled={isLoading || isSkipping}
          >
            <Text style={styles.skipButtonText}>
              {isSkipping ? 'SKIPPING...' : 'SKIP TO HOME'}
            </Text>
          </TouchableOpacity>

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>Login</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>


    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 150 : 200, // Increased padding for better keyboard handling
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    color: '#ffffff',
    marginTop: 5,
  },
  formContainer: {
    backgroundColor: '#1e1e1e',
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    elevation: 0, // Remove any shadow on Android
    shadowOpacity: 0, // Remove any shadow on iOS
  },
  label: {
    color: '#ffffff',
    fontSize: 16,
    marginBottom: 5,
    marginTop: 15,
  },
  input: {
    backgroundColor: '#333333',
    borderRadius: 5,
    padding: 15,
    color: '#ffffff',
    fontSize: 16,
  },
  dateText: {
    color: '#ffffff',
    fontSize: 16,
  },
  placeholderText: {
    color: '#666',
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#ff5252',
  },
  errorText: {
    color: '#ff5252',
    fontSize: 14,
    marginTop: 5,
  },
  authErrorContainer: {
    backgroundColor: '#2a2a2a',
    borderRadius: 5,
    padding: 10,
    marginTop: 15,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#444444',
  },
  authErrorText: {
    color: '#e0e0e0',
    fontSize: 14,
  },
  helperText: {
    color: '#999',
    fontSize: 12,
    marginTop: 5,
    marginBottom: 5,
  },
  pickerContainer: {
    backgroundColor: '#333333',
    borderRadius: 5,
    marginBottom: 10,
  },
  picker: {
    color: '#ffffff',
    height: 50,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333333',
    borderRadius: 5,
  },
  passwordInput: {
    flex: 1,
    padding: 15,
    color: '#ffffff',
    fontSize: 16,
    backgroundColor: 'transparent',
  },
  eyeIcon: {
    padding: 15,
  },
  eyeIconText: {
    fontSize: 18,
    color: '#e0e0e0',
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    marginBottom: 15,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#ffffff',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#ffffff',
  },
  checkmark: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  rememberMeText: {
    color: '#e0e0e0',
    fontSize: 14,
  },
  signupButton: {
    marginTop: 10,
    marginBottom: 10,
  },
  skipButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#666',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
    marginBottom: 20,
  },
  skipButtonText: {
    color: '#999',
    fontSize: 16,
    fontWeight: '600',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  loginText: {
    color: '#e0e0e0',
    fontSize: 16,
  },
  loginLink: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
